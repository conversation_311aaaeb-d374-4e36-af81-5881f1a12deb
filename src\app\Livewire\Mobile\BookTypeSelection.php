<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use App\Models\BookType;
use App\Models\UserBook;
use App\Services\BookDiscovery\BookDiscoveryService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BookTypeSelection extends Component
{
    public $bookData = [];
    public $bookTypes = [];
    public $selectedBookTypeId = null;
    public $manualPageCount = null;
    public $selectedStartDate = null; // For date selection widget
    public $isLoading = false;
    public $errorMessage = '';
    public $successMessage = '';

    protected $rules = [
        'selectedBookTypeId' => 'required|exists:book_types,id',
        'manualPageCount' => 'nullable|integer|min:1|max:1000',
    ];

    protected $messages = [
        'selectedBookTypeId.required' => 'Lütfen kitap türünü seçiniz.',
        'selectedBookTypeId.exists' => 'Geçersiz kitap türü seçimi.',
        'manualPageCount.integer' => 'Sayfa sayısı sayısal bir değer olmalıdır.',
        'manualPageCount.min' => '<PERSON><PERSON> sayısı en az 1 olmalıdır.',
        'manualPageCount.max' => 'Sayfa sayısı en fazla 1000 olabilir.',
    ];

    protected $listeners = ['dateSelected'];

    public function mount(array $bookData)
    {
        $this->bookData = $bookData;
        $this->selectedStartDate = now()->toDateString(); // Default to today
        $this->loadBookTypes();
    }

    /**
     * Handle date selection from the date widget
     */
    public function dateSelected($data)
    {
        $this->selectedStartDate = $data['date'];
    }

    public function loadBookTypes()
    {
        $this->bookTypes = BookType::orderBy('id')
            ->get()
            ->toArray();
    }

    public function createBook()
    {
        $this->validate();

        $this->isLoading = true;
        $this->errorMessage = '';
        $this->successMessage = '';

        try {
            // Prepare book data with user selections
            $finalBookData = $this->bookData;
            $finalBookData['book_type_id'] = $this->selectedBookTypeId;

            // Use manual page count if provided, otherwise use discovered data
            if ($this->manualPageCount) {
                $finalBookData['page_count'] = $this->manualPageCount;
            }

            // Create book using discovery service
            $discoveryService = app(BookDiscoveryService::class);
            $book = $discoveryService->createBookFromData($finalBookData, Auth::id());

            if ($book) {
                // Check if book is already in user's list
                $existingUserBook = UserBook::where('user_id', Auth::id())
                    ->where('book_id', $book->id)
                    ->first();

                if (!$existingUserBook) {
                    // Add book to user's list
                    UserBook::create([
                        'user_id' => Auth::id(),
                        'book_id' => $book->id,
                        'start_date' => $this->selectedStartDate ?: now(),
                        'created_by' => Auth::id(),
                    ]);
                }

                $this->successMessage = __('mobile.book_created_success');

                // Dispatch events
                $this->dispatch('book-created', ['book_id' => $book->id]);

                Log::info('Book created successfully via discovery service', [
                    'book_id' => $book->id,
                    'user_id' => Auth::id(),
                    'isbn' => $finalBookData['isbn'] ?? null,
                    'user_book_created' => !$existingUserBook
                ]);

                session()->flash('success', __('mobile.book_added_success') );
                return redirect()->route('mobile.books');
            } else {
                $this->errorMessage = __('mobile.failed_add_book');
            }

        } catch (\Exception $e) {
            Log::error('Failed to create book from discovery', [
                'book_data' => $this->bookData,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->errorMessage = __('mobile.failed_add_book');
        }

        $this->isLoading = false;
    }

    public function goBack()
    {
        $this->dispatch('go-back');
    }

    public function render()
    {
        return view('livewire.mobile.book-type-selection');
    }
}
