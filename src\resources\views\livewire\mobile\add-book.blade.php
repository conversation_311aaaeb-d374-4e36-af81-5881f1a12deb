<div class="min-h-screen bg-gray-50" x-data="addBookScanner">
     <x-mobile-page-header route="{{ route('mobile.books') }}" header="{{ __('mobile.add_new_book') }}" />

    <div class="p-4">
        <!-- Tab Navigation -->
        <div class="flex bg-white rounded-2xl p-1 mb-6 shadow-sm">
            <button
                wire:click="setActiveTab('class')"
                class="flex-1 py-2 px-4 rounded-xl font-semibold transition-all {{ $activeTab === 'class' ? 'bg-violet-400 text-white' : 'text-gray-600' }}"
            >
                {{ __('mobile.from_class_bookshelf') }}
            </button>
            <button
                wire:click="setActiveTab('manual')"
                class="flex-1 py-2 px-4 rounded-xl font-semibold transition-all {{ $activeTab === 'manual' ? 'bg-violet-400 text-white' : 'text-gray-600' }}"
            >
                {{ __('mobile.type_isbn_manually') }}
            </button>
            <button
                wire:click="setActiveTab('scan')"
                class="flex-1 py-2 px-4 rounded-xl font-semibold transition-all {{ $activeTab === 'scan' ? 'bg-violet-400 text-white' : 'text-gray-600' }}"
            >
                {{ __('mobile.scan_barcode') }}
            </button>
        </div>

        <!-- Class Bookshelf Section -->
        @if($activeTab === 'class')
            <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
                <h3 class="font-semibold text-gray-900 mb-4">{{ __('mobile.select_from_class_bookshelf') }}</h3>

                @if($isLoadingClassBooks)
                    <div class="text-center py-8">
                        <div class="mobile-spinner mx-auto"></div>
                        <p class="text-gray-600 mt-2">{{ __('mobile.loading_class_books') }}</p>
                    </div>
                @elseif(empty($classBooks))
                    <!-- Empty State -->
                    <div class="text-center py-12">
                        <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('mobile.no_class_books') }}</h3>
                        <p class="text-gray-600 max-w-sm mx-auto">{{ __('mobile.no_class_books_message') }}</p>
                    </div>
                @else
                    <!-- Books Grid -->
                    <div class="grid grid-cols-2 gap-4">
                        @foreach($classBooks as $book)
                            <div class="text-center">
                                <button
                                    wire:click="selectClassBook({{ $book['id'] }})"
                                    class="w-full bg-gray-50 rounded-2xl p-4 hover:bg-gray-100 transition-colors"
                                >
                                    <!-- Book Cover -->
                                    <div class="w-20 h-28 mx-auto mb-3 rounded-lg overflow-hidden shadow-md">
                                        @if($book['cover_image'])
                                            <img src="{{ asset('storage/' . $book['cover_image']) }}"
                                                 alt="{!! $book['name'] !!}"
                                                 class="w-full h-full object-cover">
                                        @else
                                            <img src="/images/default-book-cover.png"
                                                 alt="{!! $book['name'] !!}"
                                                 class="w-full h-full object-cover">
                                        @endif
                                    </div>

                                    <!-- Book Info -->
                                    <h4 class="font-semibold text-gray-900 text-sm mb-1 line-clamp-2">{!! $book['name'] !!}</h4>
                                    @if($book['authors'])
                                        <p class="text-xs text-gray-600 line-clamp-1">{{ $book['authors'] }}</p>
                                    @endif
                                </button>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        @endif

        <!-- Manual Entry Section -->
        @if($activeTab === 'manual')
            <!-- ISBN Input Section -->
            @if($searchMethod === 'isbn')
            <!-- How to find book barcode info -->
            <div class="bg-gradient-to-r from-violet-100 to-purple-100 rounded-2xl p-4 mb-6">
                <h3 class="font-semibold text-gray-900 mb-3">{{ __('mobile.how_to_find_barcode') }}</h3>
                <div class="bg-white rounded-xl p-3">
                    <img src="/images/barcode-example.jpg" alt="Book barcode location" class="w-full h-32 object-contain rounded-lg mb-2">
                    <p class="text-sm text-gray-600 text-center">{{ __('mobile.how_to_find_barcode_desc') }}</p>
                </div>
            </div>
            <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
                <div class="mb-4">
                    <div class="flex space-x-3">
                        <input
                            type="text"
                            id="isbn"
                            wire:model.live.debounce.500ms="isbn"
                            class="mobile-input flex-1"
                            placeholder="{{ __('mobile.enter_book_barcode') }}"
                            maxlength="13"
                        >
                        <button
                            type="button"
                            wire:click="searchByIsbn"
                            class="bg-violet-600 hover:bg-violet-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors {{ empty($isbn) || $isLoading ? 'opacity-50 cursor-not-allowed' : '' }}"
                            wire:loading.attr="disabled"
                            {{ empty($isbn) ? 'disabled' : '' }}
                        >
                            <span wire:loading.remove>{{ __('mobile.search_button') }}</span>
                            <span wire:loading>
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
                <!-- Loading State -->
                @if($isLoading)
                    <div class="text-center py-8">
                        <div class="mobile-spinner mx-auto"></div>
                        <p class="text-gray-600 mt-2">{{ __('mobile.searching_for_book') }}</p>
                    </div>
                @endif
            </div>
            @endif
        @endif

        <!-- Scan Barcode Section -->
        @if($activeTab === 'scan')
            <!-- Barcode Scanner Section -->
            @if($searchMethod === 'scan')
            <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
                <h3 class="font-semibold text-gray-900 mb-4">{{ __('mobile.scan_barcode') }}</h3>

                @if(!$isScanning && !$cameraInitializing)
                    <div class="text-center py-8">
                        <div class="w-20 h-20 bg-purple-100 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                            <svg class="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('mobile.ready_to_scan') }}</h3>
                        <p class="text-gray-600 mb-6">{{ __('mobile.point_camera_at_barcode') }}</p>

                        <button
                            wire:click="startScanning"
                            class="mobile-button"
                            x-bind:disabled="!cameraSupported"
                            x-bind:class="!cameraSupported ? 'opacity-50 cursor-not-allowed' : ''"
                        >
                            <span x-show="cameraSupported">{{ __('mobile.start_scanning') }}</span>
                            <span x-show="!cameraSupported">{{ __('mobile.camera_not_available') }}</span>
                        </button>

                        <div class="mt-4" x-show="!cameraSupported">
                            <p class="text-xs text-gray-500 mb-2">{{ __('mobile.camera_not_available_desc') }}</p>
                        </div>
                    </div>
                @elseif($cameraInitializing)
                    <!-- Camera Initializing State with Video Element -->
                    <div class="relative">
                        <video
                            id="barcode-scanner"
                            class="w-full h-64 bg-black rounded-xl object-cover"
                            autoplay
                            playsinline
                            muted
                        ></video>

                        <!-- Initializing Overlay -->
                        <div class="absolute inset-0 flex items-center justify-center bg-black/50">
                            <div class="text-center">
                                <div class="mobile-spinner mx-auto mb-4"></div>
                                <h3 class="text-lg font-semibold text-white mb-2">{{ __('mobile.initializing_camera') }}</h3>
                                <p class="text-gray-200">{{ __('mobile.please_allow_camera_access') }}</p>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- Camera View -->
                    <div class="relative">
                        <video
                            id="barcode-scanner"
                            class="w-full h-64 bg-black rounded-xl object-cover"
                            autoplay
                            playsinline
                            muted
                        ></video>

                        <!-- Scanning Overlay -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-48 h-32 border-2 border-white rounded-lg relative">
                                <div class="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-violet-600 rounded-tl-lg"></div>
                                <div class="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-violet-600 rounded-tr-lg"></div>
                                <div class="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-violet-600 rounded-bl-lg"></div>
                                <div class="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-violet-600 rounded-br-lg"></div>
                            </div>
                        </div>

                        <!-- Instructions -->
                        <div class="absolute bottom-4 left-0 right-0 text-center">
                            <p class="text-white text-sm bg-black/50 px-3 py-1 rounded-full inline-block">
                                {{ __('mobile.position_barcode') }}
                            </p>
                        </div>
                    </div>

                    <div class="mt-4 text-center">
                        <button
                            wire:click="stopScanning"
                            class="mobile-button-secondary"
                        >
                            {{ __('mobile.stop_scanning') }}
                        </button>
                    </div>
                @endif
            </div>
            @endif
        @endif

        <!-- Confirmation Modal for Class Books -->
        @if($showConfirmModal && $selectedClassBook)
            <div class="mobile-modal" x-data="{ show: @entangle('showConfirmModal') }" x-show="show" x-transition>
                <div class="mobile-modal-content">
                    <div class="text-center mb-6">
                        <div class="w-16 h-22 mx-auto mb-4 rounded-lg overflow-hidden shadow-md">
                            @if($selectedClassBook['cover_image'])
                                <img src="{{ asset('storage/' . $selectedClassBook['cover_image']) }}"
                                     alt="{!! $selectedClassBook['name'] !!}"
                                     class="w-full h-full object-cover">
                            @else
                                <img src="/images/default-book-cover.png"
                                     alt="{!! $selectedClassBook['name'] !!}"
                                     class="w-full h-full object-cover">
                            @endif
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{!! $selectedClassBook['name'] !!}</h3>
                        @if($selectedClassBook['authors'])
                            <p class="text-sm text-gray-600 mb-4">{{ $selectedClassBook['authors'] }}</p>
                        @endif
                        <p class="text-gray-700 font-semibold mt-2">{{ __('mobile.select_reading_start_date') }}</p>

                        <!-- Date Selection Widget -->
                        <div class="mb-4">
                            <livewire:mobile.components.date-selection-widget
                                :selectedDate="now()"
                                :minDate="now()->subDays(7)"
                                :maxDate="now()"
                                wire:key="class-book-date-selector" />
                        </div>
                    </div>

                    <div class="flex space-x-3">
                        <button
                            wire:click="cancelSelection"
                            class="flex-1 mobile-button-secondary px-4"
                        >
                            {{ __('mobile.cancel') }}
                        </button>
                        <button
                            wire:click="addClassBookToCollection"
                            class="mobile-button"
                            wire:loading.attr="disabled"
                        >
                            <span wire:loading.remove>{{ __('mobile.add_to_books') }}</span>
                            <span wire:loading class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ __('mobile.adding') }}
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        @endif

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ session('error') }}</p>
            </div>
        @endif

        <!-- Component Messages -->
        @if($successMessage)
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ $successMessage }}</p>
            </div>
        @endif

        @if($errorMessage)
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ $errorMessage }}</p>
            </div>
        @endif

        <!-- Book Type Selection Component -->
        @if($showBookTypeSelection && $discoveredBookData)
            <livewire:mobile.book-type-selection :book-data="$discoveredBookData" />
        @endif

        <!-- Selected Book Preview (for locally found books) -->
        @if($selectedBook && !$showBookTypeSelection)
            <div class="bg-white rounded-2xl p-6 shadow-sm">
                <div class="flex items-start space-x-4 mb-4">
                    <img src="{{ $selectedBook['cover_image'] ? asset('storage/' . $selectedBook['cover_image']) : '/images/default-book-cover.png' }}"
                         alt="{!! $selectedBook['name'] !!}"
                         class="mobile-book-cover">

                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-1">{!! $selectedBook['name'] !!}</h4>
                        <p class="text-sm text-gray-600 mb-2">
                            @if(isset($selectedBook['authors']) && count($selectedBook['authors']) > 0)
                                {{ collect($selectedBook['authors'])->pluck('name')->join(', ') }}
                            @else
                                {{ __('mobile.unknown_author') }}
                            @endif
                        </p>
                        @if($selectedBook['isbn'])
                            <p class="text-xs text-gray-500 mb-2">{{ __('mobile.isbn') }}: {{ $selectedBook['isbn'] }}</p>
                        @endif
                        @if($selectedBook['page_count'])
                            <p class="text-xs text-gray-500 mb-2">{{ $selectedBook['page_count'] }} pages</p>
                        @endif
                        @if(isset($selectedBook['publisher']) && $selectedBook['publisher'])
                            <p class="text-xs text-gray-500 mb-2">{{ $selectedBook['publisher']['name'] ?? $selectedBook['publisher'] }}</p>
                        @endif
                        @if($selectedBook['year'])
                            <p class="text-xs text-gray-500">{{ $selectedBook['year'] }}</p>
                        @endif
                    </div>
                </div>

                <!-- Date Selection Widget -->
                <div class="mb-4">
                    <p class="text-gray-700 mb-2 text-sm">{{ __('mobile.select_reading_start_date') }}</p>
                    <livewire:mobile.components.date-selection-widget
                        :selectedDate="now()"
                        :minDate="now()->subDays(7)"
                        :maxDate="now()"
                        wire:key="selected-book-date-selector" />
                </div>

                <div class="flex space-x-3">
                    <button
                        wire:click="addSelectedBook"
                        class="flex-1 mobile-button"
                        wire:loading.attr="disabled"
                    >
                        <span wire:loading.remove>{{ __('mobile.add_to_books') }}</span>
                        <span wire:loading class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ __('mobile.adding') }}
                        </span>
                    </button>
                    
                    <button
                        wire:click="resetSearch"
                        class="flex-1 mobile-button-secondary"
                    >
                        {{ __('mobile.search_again') }}
                    </button>
                </div>
            </div>
        @endif
    </div>
</div>
