<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserBook;
use App\Models\User;
use App\Models\Book;
use App\MoonShine\Handlers\AppImportHandler;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Date;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\ImportExport\Contracts\HasImportExportContract;
use MoonShine\Laravel\Handlers\Handler;
use MoonShine\ImportExport\Traits\ImportExportConcern;
use MoonShine\UI\Components\ActionButton;
use MoonShine\UI\Components\Link;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Number;

#[Icon('book-open')]
class UserBookResource extends BaseResource implements HasImportExportContract
{
    use ImportExportConcern;

    protected string $model = UserBook::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'book'];

    public function getTitle(): string
    {
        return __('admin.user_books');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: BookResource::class
            )
                ->sortable(),

            Date::make(__('admin.start_date'), 'start_date')   
                ->format('d.m.Y')         
                ->sortable(),

            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y')
                ->sortable(),

            Text::make(__('admin.reading_status'), 'localized_reading_status')
                ->sortable(),
/*
            Text::make(__('admin.session_info'), 'session_info')
                ->sortable(),

            Text::make(__('admin.reading_duration'), 'reading_duration_text')
                ->sortable(),

            Text::make(__('admin.progress_percentage'), 'progress_percentage_text')
                ->sortable(),
*/                
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: UserResource::class
                    )
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.book'),
                        'book',
                        formatted: fn(Book $book) => html_entity_decode($book->name) . ' - ' . $book->isbn,
                        resource: BookResource::class
                    )
                        ->required()
                        ->searchable(),
                ]),

                Flex::make([
                    Date::make(__('admin.start_date'), 'start_date')
                        ->required()
                        ->default(now()->format('Y-m-d'))
                        ->hint(__('admin.start_date_hint')),

                    Date::make(__('admin.end_date'), 'end_date')
                        ->hint(__('admin.end_date_hint')),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: BookResource::class
            ),

            Date::make(__('admin.start_date'), 'start_date'),
            Date::make(__('admin.end_date'), 'end_date'),
            Text::make(__('admin.session_info'), 'session_info'),
            Text::make(__('admin.reading_status'), 'localized_reading_status'),
            Text::make(__('admin.reading_duration'), 'reading_duration_text'),
            Text::make(__('admin.progress_percentage'), 'progress_percentage_text'),
            Text::make(__('admin.total_pages_read'), 'total_pages_read'),
            Text::make(__('admin.total_reading_time'), 'total_reading_time'),
            Text::make(__('admin.has_reading_logs'), 'has_reading_logs'),
            Text::make(__('admin.reading_history'), 'reading_history'),
            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'user_id' => ['required', 'exists:users,id'],
            'book_id' => ['required', 'exists:books,id'],
            'start_date' => ['required', 'date', 'before_or_equal:today'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date', 'before_or_equal:today'],
            ...parent::getCommonRules($item),
        ];

        // Add custom validation for new sessions
        if (!$item || !$item->id) {
            $rules['user_id'][] = function ($attribute, $value, $fail) {
                $bookId = request('book_id');
                if ($bookId && !UserBook::canStartNewSession($value, $bookId)) {
                    $fail(__('admin.cannot_start_new_session_active_exists'));
                }
            };
        }

        return $rules;
    }

    protected function search(): array
    {
        return ['user.name', 'user.email', 'book.name', 'book.isbn'];
    }

    protected function getDefaultSort(): array
    {
        return ['start_date' => 'desc', 'user.name' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all user books
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see books for students in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            });
        }

        // Teachers can see books for students in their assigned classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            });
        }

        // Students can only see their own books
        if ($user->isStudent()) {
            return $builder->where('user_id', $user->id);
        }

        // Default: no access
        return $builder->where('id', 0);
    }

    protected function import(): ?Handler
    {
        set_time_limit(600);
        return AppImportHandler::make(__('moonshine::ui.import'))
            ->delimiter(';')
            ->templateLink(asset('storage/templates/user_book_import_template.xlsx'));
    }   

    protected function importFields(): iterable
    {
        return [
            ID::make(),
            BelongsTo::make(__('admin.username'),'user',resource: UserResource::class)
                ->fromRaw(static fn(string $raw, $ctx) => User::where('username', $raw)->first()->id),

            BelongsTo::make(__('admin.book_isbn'),'book',resource: BookResource::class)
                ->fromRaw(static fn(int $raw, $ctx) => Book::where('isbn', $raw)->first()->id),

            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y'),

            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y'),

            Number::make(__('admin.completed'), 'completed'),
        ];
    }

    public function afterImported(mixed $item): mixed
    {
        // create user reading logs for each day of the session
        if ($item->completed) {
            $item->createReadingLogsForSession();
        }   
        return $item;
    }      

    protected function exportFields(): iterable
    {
        return [
            ID::make(),
            BelongsTo::make(__('admin.user'),'user', resource: UserResource::class)
                ->modifyRawValue(static fn($raw, $data, $ctx) =>  User::where('id', $raw)->first()->name),
            BelongsTo::make(__('admin.book'),'book', resource: BookResource::class)
                ->modifyRawValue(static fn($raw, $data, $ctx) => Book::where('id', $raw)->first()->isbn . ' - ' . Book::where('id', $raw)->first()->name),
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y'),
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y'),
            Number::make(__('admin.completed'), 'completed'),
        ];
    }

}
