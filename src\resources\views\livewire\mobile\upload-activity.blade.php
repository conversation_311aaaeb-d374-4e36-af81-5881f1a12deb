<div class="min-h-screen bg-base-200">
     <x-mobile-page-header route="{{ route('mobile.books.activities', $book->id) }}" header="{{ __('mobile.upload_activity') }}" />

    <div class="p-4">
        <!-- Activity Info -->
        <div class="bg-white rounded-2xl p-4 mb-4 shadow-sm">
            <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center">
                    <span class="text-3xl">🎨</span>
                </div>
                <h3 class="font-semibold text-gray-900 mb-1">{{ $activity->name }}</h3>
            </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-2">{{ $activity->description }}</p>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="mobile-badge">{{ $activity->points }} {{ __('mobile.points_localizable') }}</span>
                        <span class="text-gray-500">📖 {!! $book->name !!}</span>
                    </div>
                </div>

        </div>

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ session('error') }}</p>
            </div>
        @endif

        <!-- Upload Form -->
        <div class="bg-white rounded-2xl p-4 shadow-sm">
            <form wire:submit="submitActivity" class="space-y-6">
                <div>
                    @if($mediaFile)
                        <!-- Preview -->
                        <div class="mb-4">
                            @if($activity->media_type === 2)
                                <!-- Audio Preview -->
                                <div class="bg-gray-100 rounded-xl p-4 text-center">
                                    <div class="w-16 h-16 bg-purple-100 rounded-2xl mx-auto mb-2 flex items-center justify-center">
                                        <span class="text-2xl">🎵</span>
                                    </div>
                                    <p class="text-sm text-gray-600">{{ $mediaFile->getClientOriginalName() }}</p>
                                </div>
                            @else
                                <!-- Image Preview -->
                                <img src="{{ $mediaFile->temporaryUrl() }}" class="w-full h-48 object-contain rounded-xl">
                            @endif
                            <button
                                type="button"
                                wire:click="$set('mediaFile', null)"
                                class="mt-2 text-red-500 text-sm hover:text-red-700"
                            >
                                🗑️ {{ __('mobile.remove_file') }}
                            </button>
                        </div>
                    @else
                        @if($activity->media_type === 2)
                            <!-- Audio Recording Interface -->
                            <div x-data="audioRecorder" class="space-y-4">
                                <!-- Permission Error -->
                                <div x-show="permissionError" class="bg-red-50 border border-red-200 rounded-xl p-4">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-red-500">🎤</span>
                                        <p class="text-red-600 text-sm" x-text="permissionError"></p>
                                    </div>
                                </div>

                                <!-- Recording Interface -->
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center hover:border-primary transition-colors">
                                     <!-- Recording Title -->
                                    <h4 class="text-lg font-semibold text-gray-900 mb-2">
                                        <span x-show="!isRecording && !hasRecording">{{ __('mobile.record_audio') }}</span>
                                        <span x-show="isRecording">{{ __('mobile.recording') }}...</span>
                                        <span x-show="hasRecording && !isRecording">{{ __('mobile.recording_complete') }}</span>
                                    </h4>

                                    <!-- Recording Duration -->
                                    <p x-show="isRecording" class="text-purple-600 font-mono text-lg mb-4" x-text="formatTime(recordingDuration)"></p>

                                    <!-- Recording Controls -->
                                    <div class="flex justify-center space-x-4 mb-4">
                                        <!-- Record/Stop Button -->
                                        <button
                                            type="button"
                                            @click="toggleRecording"
                                            :disabled="permissionError"
                                            class="flex items-center justify-center w-16 h-16 rounded-full transition-colors"
                                            :class="isRecording ? 'bg-red-500 hover:bg-red-600 text-white' : 'bg-purple-600 hover:bg-purple-700 text-white'"
                                        >
                                            <svg x-show="!isRecording" class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                                                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                                            </svg>
                                            <div x-show="isRecording" class="w-4 h-4 bg-white rounded-sm"></div>
                                        </button>

                                        <!-- Play Button -->
                                        <button
                                            x-show="hasRecording && !isRecording"
                                            type="button"
                                            @click="togglePlayback"
                                            class="flex items-center justify-center w-16 h-16 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors"
                                        >
                                            <svg x-show="!isPlaying" class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M8 5v14l11-7z"/>
                                            </svg>
                                            <svg x-show="isPlaying" class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                                            </svg>
                                        </button>

                                        <!-- Delete Button -->
                                        <button
                                            x-show="hasRecording && !isRecording"
                                            type="button"
                                            @click="deleteRecording"
                                            class="flex items-center justify-center w-16 h-16 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
                                        >
                                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                            </svg>
                                        </button>
                                    </div>

                                    <!-- Upload Button -->
                                    <button
                                        x-show="hasRecording && !isRecording && !$wire.mediaFile"
                                        type="button"
                                        @click="uploadRecording"
                                        :disabled="isUploading"
                                        class="mobile-button bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
                                        :class="isUploading ? 'opacity-50 cursor-not-allowed' : ''"
                                    >
                                        <span x-show="!isUploading">{{ __('mobile.upload_recording') }}</span>
                                        <span x-show="isUploading" class="flex items-center justify-center">
                                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            {{ __('mobile.uploading') }}
                                        </span>
                                    </button>

                                    <!-- Instructions -->
                                    <p class="text-gray-600 text-sm">
                                        <span x-show="!hasRecording && !isRecording">{{ __('mobile.tap_record_to_start') }}</span>
                                        <span x-show="isRecording">{{ __('mobile.tap_stop_to_finish') }}</span>
                                        <span x-show="hasRecording && !isRecording && !$wire.mediaFile">{{ __('mobile.recording_ready_upload') }}</span>
                                        <span x-show="hasRecording && $wire.mediaFile">{{ __('mobile.recording_uploaded_ready_submit') }}</span>
                                    </p>

                                    <!-- File Upload Fallback -->
                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                        <input
                                            type="file"
                                            id="mediaFile"
                                            wire:model="mediaFile"
                                            accept="audio/*,.mp3,.wav,.m4a,.aac,.webm"
                                            class="hidden"
                                        >
                                        <label for="mediaFile" class="cursor-pointer text-purple-600 hover:text-purple-700 text-sm">
                                            {{ __('mobile.or_upload_audio_file') }}
                                        </label>
                                        <p class="text-xs text-gray-500 mt-1">{{ __('mobile.mp3_wav_m4a_aac_webm_up_to_10mb') }}</p>
                                    </div>
                                </div>
                            </div>
                        @else
                            <!-- Image Upload Area -->
                            <div class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-primary transition-colors">
                                <input
                                    type="file"
                                    id="mediaFile"
                                    wire:model="mediaFile"
                                    accept="image/*"
                                    class="hidden"
                                >
                                <label for="mediaFile" class="cursor-pointer">
                                    <div class="w-16 h-16 bg-purple-100 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ __('mobile.upload_photo') }}</h4>
                                    <p class="text-gray-600">{{ __('mobile.tap_to_select') }}</p>
                                    <p class="text-sm text-gray-500 mt-2">{{ __('mobile.jpg_png_5mb') }}</p>
                                </label>
                            </div>
                        @endif
                    @endif

                    @error('mediaFile')
                        <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('mobile.describe_artwork') }}
                    </label>
                    <textarea
                        id="description"
                        wire:model="description"
                        class="mobile-input @error('description') border-red-500 @enderror"
                        placeholder="{{ __('mobile.tell_about_artwork') }}"
                        rows="2"
                        maxlength="500"
                    ></textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">{{ strlen($description) }}/500 {{ __('mobile.characters') }}</p>
                </div>

                <!-- Submit Button -->
                @if($mode !== 'view')
                    <button
                        type="submit"
                        class="mobile-button {{ $isLoading || (!$mediaFile && !$existingMediaUrl) ? 'opacity-50 cursor-not-allowed' : '' }}"
                        wire:loading.attr="disabled"
                        {{ (!$mediaFile && !$existingMediaUrl) ? 'disabled' : '' }}
                    >
                        <span wire:loading.remove">
                            @if($mode === 'edit')
                                {{ __('mobile.update_submission') }}
                            @else
                                {{ __('mobile.submit_artwork') }}
                            @endif
                        </span>
                    <span wire:loading class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                    </button>

                    @if(!$mediaFile && !$existingMediaUrl)
                        <p class="text-sm text-gray-500 text-center">
                            {{ __('mobile.upload_file_to_submit') }}
                        </p>
                    @endif
                @endif
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('audioRecorder', () => ({
        // State variables
        isRecording: false,
        hasRecording: false,
        isPlaying: false,
        isUploading: false,
        permissionError: null,
        recordingDuration: 0,

        // MediaRecorder and audio elements
        mediaRecorder: null,
        audioChunks: [],
        audioBlob: null,
        audioUrl: null,
        audioElement: null,
        stream: null,

        // Timer for recording duration
        recordingTimer: null,

        init() {
            console.log('Audio recorder component initialized');
            this.checkBrowserSupport();
        },

        checkBrowserSupport() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                this.permissionError = '{{ __("mobile.browser_not_support_audio_recording") }}';
                return false;
            }

            if (!window.MediaRecorder) {
                this.permissionError = '{{ __("mobile.browser_not_support_media_recorder") }}';
                return false;
            }

            return true;
        },

        async requestMicrophonePermission() {
            try {
                this.stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });
                this.permissionError = null;
                return true;
            } catch (error) {
                console.error('Microphone permission error:', error);

                if (error.name === 'NotAllowedError') {
                    this.permissionError = '{{ __("mobile.microphone_permission_denied") }}';
                } else if (error.name === 'NotFoundError') {
                    this.permissionError = '{{ __("mobile.no_microphone_found") }}';
                } else {
                    this.permissionError = '{{ __("mobile.microphone_access_error") }}';
                }
                return false;
            }
        },

        async toggleRecording() {
            if (this.isRecording) {
                this.stopRecording();
            } else {
                await this.startRecording();
            }
        },

        async startRecording() {
            if (!this.checkBrowserSupport()) {
                return;
            }

            // Request microphone permission
            const hasPermission = await this.requestMicrophonePermission();
            if (!hasPermission) {
                return;
            }

            try {
                // Configure MediaRecorder for WebM with Opus codec
                const options = {
                    mimeType: 'audio/webm;codecs=opus'
                };

                // Fallback for browsers that don't support WebM/Opus
                if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                    if (MediaRecorder.isTypeSupported('audio/webm')) {
                        options.mimeType = 'audio/webm';
                    } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
                        options.mimeType = 'audio/mp4';
                    } else {
                        options.mimeType = '';
                    }
                }

                this.mediaRecorder = new MediaRecorder(this.stream, options);
                this.audioChunks = [];

                this.mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        this.audioChunks.push(event.data);
                    }
                };

                this.mediaRecorder.onstop = () => {
                    this.processRecording();
                };

                this.mediaRecorder.onerror = (event) => {
                    console.error('MediaRecorder error:', event.error);
                    this.permissionError = '{{ __("mobile.recording_error") }}';
                    this.stopRecording();
                };

                // Start recording
                this.mediaRecorder.start(1000); // Collect data every second
                this.isRecording = true;
                this.recordingDuration = 0;

                // Start timer
                this.recordingTimer = setInterval(() => {
                    this.recordingDuration++;
                }, 1000);

                console.log('Recording started with MIME type:', options.mimeType);
            } catch (error) {
                console.error('Failed to start recording:', error);
                this.permissionError = '{{ __("mobile.failed_to_start_recording") }}';
            }
        },

        stopRecording() {
            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
                this.mediaRecorder.stop();
            }

            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop());
                this.stream = null;
            }

            if (this.recordingTimer) {
                clearInterval(this.recordingTimer);
                this.recordingTimer = null;
            }

            this.isRecording = false;
        },

        processRecording() {
            if (this.audioChunks.length === 0) {
                console.error('No audio data recorded');
                return;
            }

            // Create blob from recorded chunks
            this.audioBlob = new Blob(this.audioChunks, {
                type: this.mediaRecorder.mimeType || 'audio/webm'
            });

            // Create URL for playback
            if (this.audioUrl) {
                URL.revokeObjectURL(this.audioUrl);
            }
            this.audioUrl = URL.createObjectURL(this.audioBlob);

            this.hasRecording = true;

            console.log('Recording processed:', {
                size: this.audioBlob.size,
                type: this.audioBlob.type,
                duration: this.recordingDuration
            });
        },

        uploadRecording() {
            if (!this.audioBlob) {
                console.error('No audio recording to upload');
                return;
            }

            this.isUploading = true;
            this.permissionError = null;

            // Create a File object from the blob
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const extension = this.getFileExtension(this.audioBlob.type);
            const fileName = `recording-${timestamp}.${extension}`;

            const file = new File([this.audioBlob], fileName, {
                type: this.audioBlob.type,
                lastModified: Date.now()
            });

            // Upload to Livewire
            this.$wire.upload('mediaFile', file, (uploadedFilename) => {
                console.log('Audio recording uploaded successfully:', uploadedFilename);
                this.isUploading = false;
            }, (error) => {
                console.error('Failed to upload recording:', error);
                this.permissionError = '{{ __("mobile.failed_to_upload_recording") }}';
                this.isUploading = false;
            });
        },

        getFileExtension(mimeType) {
            const mimeToExt = {
                'audio/webm': 'webm',
                'audio/mp4': 'm4a',
                'audio/mpeg': 'mp3',
                'audio/wav': 'wav',
                'audio/ogg': 'ogg'
            };
            return mimeToExt[mimeType] || 'webm';
        },

        togglePlayback() {
            if (!this.audioUrl) {
                return;
            }

            if (this.isPlaying) {
                this.stopPlayback();
            } else {
                this.startPlayback();
            }
        },

        startPlayback() {
            if (this.audioElement) {
                this.audioElement.pause();
                this.audioElement = null;
            }

            this.audioElement = new Audio(this.audioUrl);

            this.audioElement.onended = () => {
                this.isPlaying = false;
            };

            this.audioElement.onerror = (error) => {
                console.error('Audio playback error:', error);
                this.isPlaying = false;
            };

            this.audioElement.play()
                .then(() => {
                    this.isPlaying = true;
                })
                .catch((error) => {
                    console.error('Failed to play audio:', error);
                });
        },

        stopPlayback() {
            if (this.audioElement) {
                this.audioElement.pause();
                this.audioElement.currentTime = 0;
            }
            this.isPlaying = false;
        },

        deleteRecording() {
            // Stop playback if playing
            this.stopPlayback();

            // Clean up URLs and objects
            if (this.audioUrl) {
                URL.revokeObjectURL(this.audioUrl);
                this.audioUrl = null;
            }

            if (this.audioElement) {
                this.audioElement = null;
            }

            // Reset state
            this.audioBlob = null;
            this.audioChunks = [];
            this.hasRecording = false;
            this.isUploading = false;
            this.recordingDuration = 0;
            this.permissionError = null;

            // Clear Livewire file
            this.$wire.set('mediaFile', null);

            console.log('Recording deleted');
        },

        formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        },

        // Cleanup when component is destroyed
        destroy() {
            this.stopRecording();
            this.stopPlayback();

            if (this.audioUrl) {
                URL.revokeObjectURL(this.audioUrl);
            }
        }
    }));
});
</script>
