<?php

use App\Models\{User, Book, UserBook, Publisher, BookType};
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

echo "=== Date Selection Widget Implementation Test ===\n\n";

// Test Case 1: Verify Date Selection Widget Component exists and works
echo "TEST CASE 1: Date Selection Widget Component\n";

try {
    $widgetClass = 'App\\Livewire\\Mobile\\Components\\DateSelectionWidget';
    if (class_exists($widgetClass)) {
        echo "✅ DateSelectionWidget component exists\n";
        
        // Test widget initialization
        $widget = new $widgetClass();
        $widget->mount();
        
        echo "✅ Widget can be instantiated and mounted\n";
        
        // Test date navigation methods
        if (method_exists($widget, 'previousDate') && method_exists($widget, 'nextDate')) {
            echo "✅ Navigation methods exist\n";
        } else {
            echo "❌ Navigation methods missing\n";
        }
        
        // Test date display method
        if (method_exists($widget, 'getDateDisplayText')) {
            echo "✅ Date display method exists\n";
        } else {
            echo "❌ Date display method missing\n";
        }
        
    } else {
        echo "❌ DateSelectionWidget component not found\n";
    }
} catch (Exception $e) {
    echo "❌ Error testing widget: " . $e->getMessage() . "\n";
}

echo "\n";

// Test Case 2: Verify AddBook component integration
echo "TEST CASE 2: AddBook Component Integration\n";

try {
    $addBookClass = 'App\\Livewire\\Mobile\\AddBook';
    if (class_exists($addBookClass)) {
        $addBook = new $addBookClass();
        
        // Check if selectedStartDate property exists
        if (property_exists($addBook, 'selectedStartDate')) {
            echo "✅ selectedStartDate property exists in AddBook\n";
        } else {
            echo "❌ selectedStartDate property missing in AddBook\n";
        }
        
        // Check if dateSelected method exists
        if (method_exists($addBook, 'dateSelected')) {
            echo "✅ dateSelected method exists in AddBook\n";
        } else {
            echo "❌ dateSelected method missing in AddBook\n";
        }
        
        // Check if listeners include dateSelected
        $reflection = new ReflectionClass($addBook);
        if ($reflection->hasProperty('listeners')) {
            $listenersProperty = $reflection->getProperty('listeners');
            $listenersProperty->setAccessible(true);
            $listeners = $listenersProperty->getValue($addBook);
            
            if (is_array($listeners) && in_array('dateSelected', $listeners)) {
                echo "✅ dateSelected listener configured in AddBook\n";
            } else {
                echo "❌ dateSelected listener not configured in AddBook\n";
            }
        }
        
    } else {
        echo "❌ AddBook component not found\n";
    }
} catch (Exception $e) {
    echo "❌ Error testing AddBook: " . $e->getMessage() . "\n";
}

echo "\n";

// Test Case 3: Verify ReadingLog component integration
echo "TEST CASE 3: ReadingLog Component Integration\n";

try {
    $readingLogClass = 'App\\Livewire\\Mobile\\ReadingLog';
    if (class_exists($readingLogClass)) {
        $readingLog = new $readingLogClass();
        
        // Check if selectedLogDate property exists
        if (property_exists($readingLog, 'selectedLogDate')) {
            echo "✅ selectedLogDate property exists in ReadingLog\n";
        } else {
            echo "❌ selectedLogDate property missing in ReadingLog\n";
        }
        
        // Check if dateSelected method exists
        if (method_exists($readingLog, 'dateSelected')) {
            echo "✅ dateSelected method exists in ReadingLog\n";
        } else {
            echo "❌ dateSelected method missing in ReadingLog\n";
        }
        
        // Check if listeners include dateSelected
        $reflection = new ReflectionClass($readingLog);
        if ($reflection->hasProperty('listeners')) {
            $listenersProperty = $reflection->getProperty('listeners');
            $listenersProperty->setAccessible(true);
            $listeners = $listenersProperty->getValue($readingLog);
            
            if (is_array($listeners) && in_array('dateSelected', $listeners)) {
                echo "✅ dateSelected listener configured in ReadingLog\n";
            } else {
                echo "❌ dateSelected listener not configured in ReadingLog\n";
            }
        }
        
    } else {
        echo "❌ ReadingLog component not found\n";
    }
} catch (Exception $e) {
    echo "❌ Error testing ReadingLog: " . $e->getMessage() . "\n";
}

echo "\n";

// Test Case 4: Verify language translations
echo "TEST CASE 4: Language Translations\n";

$englishTranslations = [
    'days_ago',
    'select_reading_start_date',
    'date_required',
    'invalid_date',
    'date_cannot_be_future',
    'date_cannot_be_before_book_start'
];

$turkishTranslations = [
    'days_ago',
    'select_reading_start_date',
    'date_required',
    'invalid_date',
    'date_cannot_be_future',
    'date_cannot_be_before_book_start'
];

try {
    $englishFile = base_path('src/lang/en/mobile.php');
    $turkishFile = base_path('src/lang/tr/mobile.php');
    
    if (file_exists($englishFile)) {
        $englishLang = include $englishFile;
        $missingEnglish = [];
        
        foreach ($englishTranslations as $key) {
            if (!isset($englishLang[$key])) {
                $missingEnglish[] = $key;
            }
        }
        
        if (empty($missingEnglish)) {
            echo "✅ All English translations present\n";
        } else {
            echo "❌ Missing English translations: " . implode(', ', $missingEnglish) . "\n";
        }
    } else {
        echo "❌ English translation file not found\n";
    }
    
    if (file_exists($turkishFile)) {
        $turkishLang = include $turkishFile;
        $missingTurkish = [];
        
        foreach ($turkishTranslations as $key) {
            if (!isset($turkishLang[$key])) {
                $missingTurkish[] = $key;
            }
        }
        
        if (empty($missingTurkish)) {
            echo "✅ All Turkish translations present\n";
        } else {
            echo "❌ Missing Turkish translations: " . implode(', ', $missingTurkish) . "\n";
        }
    } else {
        echo "❌ Turkish translation file not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking translations: " . $e->getMessage() . "\n";
}

echo "\n";

// Test Case 5: Verify Blade templates
echo "TEST CASE 5: Blade Template Integration\n";

$templates = [
    'src/resources/views/livewire/mobile/components/date-selection-widget.blade.php',
    'src/resources/views/livewire/mobile/add-book.blade.php',
    'src/resources/views/livewire/mobile/reading-log.blade.php'
];

foreach ($templates as $template) {
    $fullPath = base_path($template);
    if (file_exists($fullPath)) {
        echo "✅ Template exists: " . basename($template) . "\n";
        
        $content = file_get_contents($fullPath);
        
        // Check for widget usage in templates
        if (strpos($content, 'livewire:mobile.components.date-selection-widget') !== false) {
            echo "  ✅ Widget component usage found\n";
        } elseif (basename($template) === 'date-selection-widget.blade.php') {
            echo "  ✅ Widget template (no self-reference needed)\n";
        } else {
            echo "  ❌ Widget component usage not found\n";
        }
        
    } else {
        echo "❌ Template missing: " . basename($template) . "\n";
    }
}

echo "\n=== Test Summary ===\n";
echo "Date Selection Widget implementation test completed.\n";
echo "Review the results above to ensure all components are properly integrated.\n";
