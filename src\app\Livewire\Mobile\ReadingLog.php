<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Book;
use App\Models\UserBook;
use App\Models\UserReadingLog;
use Carbon\Carbon;

class ReadingLog extends Component
{
    public $book;
    public $userBook;
    public $readingLogs;

    // Form fields
    public $selectedLogDate = null; // For date selection widget
    public $pagesRead = '';
    public $minutesRead = '';
    public $promiseChecked = false;
    public $isLoading = false;

    // Daily limits
    const DAILY_PAGES_LIMIT = 600;
    const DAILY_MINUTES_LIMIT = 360;

    protected $listeners = ['dateSelected'];

    public function mount($book)
    {
        $this->book = Book::with(['authors', 'publisher'])->findOrFail($book);
        $this->userBook = UserBook::where('user_id', Auth::id())
            ->where('book_id', $this->book->id)
            ->first();

        // Initialize selected log date to today
        $this->selectedLogDate = now()->toDateString();

        $this->loadReadingLogs();
    }

    /**
     * Handle date selection from the date widget
     */
    public function dateSelected($data)
    {
        $this->selectedLogDate = $data['date'];
    }

    public function addReadingLog()
    {
        $this->validate([
            'selectedLogDate' => 'required|date|before_or_equal:today',
            'pagesRead' => 'required|integer|min:1',
            'minutesRead' => 'nullable|integer|min:0',
            'promiseChecked' => 'required|accepted',
        ], [
            'promiseChecked.required' => __('mobile.promise_required'),
            'promiseChecked.accepted' => __('mobile.promise_accepted'),
            'selectedLogDate.required' => __('mobile.date_required'),
            'selectedLogDate.date' => __('mobile.invalid_date'),
            'selectedLogDate.before_or_equal' => __('mobile.date_cannot_be_future'),
        ]);

        // Additional validation: ensure selected date is not before book start date
        if ($this->userBook && $this->selectedLogDate) {
            $selectedDate = Carbon::parse($this->selectedLogDate);
            $bookStartDate = Carbon::parse($this->userBook->start_date);

            if ($selectedDate->lessThan($bookStartDate)) {
                $this->addError('selectedLogDate', __('mobile.date_cannot_be_before_book_start'));
                $this->isLoading = false;
                return;
            }
        }

        $this->isLoading = true;

        try {
            // Check if book is active
            if (!$this->book->canCreateReadingLogs()) {
                session()->flash('error', __('mobile.book_inactive_warning'));
                $this->isLoading = false;
                return;
            }

            $logDate = Carbon::parse($this->selectedLogDate);

            // Check if book is already completed
            if (UserReadingLog::isBookCompletedByUser(Auth::id(), $this->book->id)) {
                session()->flash('error', __('mobile.book_already_completed'));
                $this->isLoading = false;
                return;
            }

            // Validate daily limits
            $limitErrors = $this->validateDailyLimits($this->pagesRead, $this->minutesRead, $logDate);
            if (!empty($limitErrors)) {
                $this->handleDailyLimitErrors($limitErrors);
                $this->isLoading = false;
                return;
            }

            // Check if this log will complete the book
            $totalPagesRead = $this->getTotalPagesRead();
            $newTotalPages = $totalPagesRead + $this->pagesRead;
            $bookCompleted = false;

            if ($newTotalPages >= ($this->book->page_count ?? 0)) {
                $this->pagesRead = max(0, ($this->book->page_count ?? 0) - $totalPagesRead);
                $bookCompleted = true;
            }

            $readingLog = UserReadingLog::create([
                'user_id' => Auth::id(),
                'book_id' => $this->book->id,
                'log_date' => $logDate,
                'pages_read' => $this->pagesRead,
                'reading_duration' => $this->minutesRead ?: null,
                'book_completed' => $bookCompleted,
            ]);

            $this->loadReadingLogs();
            $this->resetForm();

            // Check for rewards and handle potential redirect
            $rewardRedirect = $this->checkForRewards($readingLog, $bookCompleted);
            if ($rewardRedirect) {
                return $rewardRedirect;
            }

            if ($bookCompleted) {
                session()->flash('success', __('mobile.book_completed_success'));
                $this->isLoading = false;
                return redirect()->route('mobile.books.activities', $this->book->id);
            } else {
                session()->flash('success', __('mobile.log_added_success'));
            }
        } catch (\Exception $e) {
            Log::error('Failed to add reading log', [
                'user_id' => Auth::id(),
                'book_id' => $this->book->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            session()->flash('error', __('mobile.failed_add_log'));
        }

        $this->isLoading = false;
    }

    public function completeBook()
    {
        $this->validate([
            'selectedLogDate' => 'required|date|before_or_equal:today',
            'minutesRead' => 'nullable|integer|min:1',
            'promiseChecked' => 'required|accepted',
        ], [
            'promiseChecked.required' => __('mobile.promise_required'),
            'promiseChecked.accepted' => __('mobile.promise_accepted'),
        ]);

        $this->isLoading = true;

        try {
            // Check if book is already completed
            if (UserReadingLog::isBookCompletedByUser(Auth::id(), $this->book->id)) {
                session()->flash('error', __('mobile.book_already_completed'));
                $this->isLoading = false;
                return;
            }

            $logDate = Carbon::parse($this->selectedLogDate);
            $totalPagesRead = $this->getTotalPagesRead();
            $remainingPages = max(0, ($this->book->page_count ?? 0) - $totalPagesRead);

            if ($remainingPages <= 0) {
                session()->flash('error', __('mobile.book_already_completed'));
                $this->isLoading = false;
                return;
            }

            // Validate daily limits for book completion
            $limitErrors = $this->validateDailyLimits($remainingPages, $this->minutesRead, $logDate);
            if (!empty($limitErrors)) {
                $this->handleDailyLimitErrors($limitErrors);
                $this->isLoading = false;
                return;
            }

            $readingLog = UserReadingLog::create([
                'user_id' => Auth::id(),
                'book_id' => $this->book->id,
                'log_date' => $logDate,
                'pages_read' => $remainingPages,
                'reading_duration' => $this->minutesRead ?: null,
                'book_completed' => true,
            ]);

            $this->loadReadingLogs();

            // Check for newly unlocked rewards and handle potential redirect
            $rewardRedirect = $this->checkForRewards($readingLog, true);
            if ($rewardRedirect) {
                return $rewardRedirect;
            }

            // Set success message and redirect
            session()->flash('success', __('mobile.book_completed_success'));
            $this->isLoading = false;
            return redirect()->route('mobile.books.activities', $this->book->id);

        } catch (\Exception $e) {
            Log::error('Failed to complete book', [
                'user_id' => Auth::id(),
                'book_id' => $this->book->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            session()->flash('error', __('mobile.failed_to_complete_book'));
            $this->isLoading = false;
        }
    }

    private function resetForm()
    {
        $this->pagesRead = '';
        $this->minutesRead = '';
        $this->selectedLogDate = now()->toDateString();
        $this->promiseChecked = false;
    }

    private function checkForRewards($readingLog, $bookCompleted = false)
    {
        $rewardService = app(\App\Services\MobileRewardDisplayService::class);

        // Check for newly unlocked rewards and levels
        $rewardResult = $rewardService->checkForRewards($readingLog->id, null, $bookCompleted);

        if ($rewardResult && $rewardResult['redirect_to_celebration']) {
            // Set appropriate redirect route after celebration
            if ($bookCompleted) {
                $rewardService->setRedirectRoute('mobile.books.activities', [$this->book->id]);
            } else {
                $rewardService->setRedirectRoute('mobile.books.log', [$this->book->id]);
            }

            return redirect()->route('mobile.badge-unlocked');
        } elseif ($bookCompleted) {
            // No rewards or levels but book completed, go to activities
            return redirect()->route('mobile.books.activities', $this->book->id);
        }

        return null;
    }

    public function deleteLog($logId)
    {
        $log = UserReadingLog::where('id', $logId)
            ->where('user_id', Auth::id())
            ->first();

        if ($log) {
            // Check if this log can be deleted (only most recent log can be deleted)
            if (!$log->canBeDeleted()) {
                session()->flash('error', __('admin.can_only_delete_most_recent_reading_log'));
                return;
            }

            $log->delete();
            $this->loadReadingLogs();
            session()->flash('success', __('mobile.log_deleted_success'));
        }
    }

    private function loadReadingLogs()
    {
        $this->readingLogs = UserReadingLog::where('user_id', Auth::id())
            ->where('book_id', $this->book->id)
            ->orderBy('log_date', 'desc')
            ->get();
    }

    public function getTotalPagesRead()
    {
        return $this->readingLogs->sum('pages_read');
    }

    /**
     * Get today's total pages read across all books
     */
    private function getTodaysTotalPages($date = null)
    {
        $targetDate = $date ? Carbon::parse($date)->toDateString() : now()->toDateString();

        return UserReadingLog::where('user_id', Auth::id())
            ->whereDate('log_date', $targetDate)
            ->sum('pages_read');
    }

    /**
     * Get today's total minutes read across all books
     */
    private function getTodaysTotalMinutes($date = null)
    {
        $targetDate = $date ? Carbon::parse($date)->toDateString() : now()->toDateString();

        return UserReadingLog::where('user_id', Auth::id())
            ->whereDate('log_date', $targetDate)
            ->sum('reading_duration');
    }

    /**
     * Validate daily reading limits
     */
    private function validateDailyLimits($newPages, $newMinutes, $logDate)
    {
        $targetDate = Carbon::parse($logDate)->toDateString();
        $todaysPagesRead = $this->getTodaysTotalPages($targetDate);
        $todaysMinutesRead = $this->getTodaysTotalMinutes($targetDate);

        $totalPages = $todaysPagesRead + $newPages;
        $totalMinutes = $todaysMinutesRead + ($newMinutes ?: 0);

        $errors = [];

        // Check pages limit
        if ($totalPages > self::DAILY_PAGES_LIMIT) {
            $remainingPages = max(0, self::DAILY_PAGES_LIMIT - $todaysPagesRead);
            $errors['pages'] = [
                'exceeded' => true,
                'limit' => self::DAILY_PAGES_LIMIT,
                'current' => $todaysPagesRead,
                'remaining' => $remainingPages,
                'attempted' => $newPages
            ];
        }

        // Check minutes limit
        if ($totalMinutes > self::DAILY_MINUTES_LIMIT) {
            $remainingMinutes = max(0, self::DAILY_MINUTES_LIMIT - $todaysMinutesRead);
            $errors['minutes'] = [
                'exceeded' => true,
                'limit' => self::DAILY_MINUTES_LIMIT,
                'current' => $todaysMinutesRead,
                'remaining' => $remainingMinutes,
                'attempted' => $newMinutes
            ];
        }

        return $errors;
    }

    /**
     * Handle daily limit validation errors
     */
    private function handleDailyLimitErrors($errors)
    {
        if (isset($errors['pages']) && isset($errors['minutes'])) {
            // Both limits exceeded
            session()->flash('error', __('mobile.daily_limits_both_exceeded', [
                'pages_limit' => self::DAILY_PAGES_LIMIT,
                'minutes_limit' => self::DAILY_MINUTES_LIMIT,
                'pages_remaining' => $errors['pages']['remaining'],
                'minutes_remaining' => $errors['minutes']['remaining']
            ]));
        } elseif (isset($errors['pages'])) {
            // Pages limit exceeded
            session()->flash('error', __('mobile.daily_pages_limit_exceeded', [
                'limit' => self::DAILY_PAGES_LIMIT,
                'current' => $errors['pages']['current'],
                'remaining' => $errors['pages']['remaining'],
                'attempted' => $errors['pages']['attempted']
            ]));
        } elseif (isset($errors['minutes'])) {
            // Minutes limit exceeded
            session()->flash('error', __('mobile.daily_minutes_limit_exceeded', [
                'limit' => self::DAILY_MINUTES_LIMIT,
                'current' => $errors['minutes']['current'],
                'remaining' => $errors['minutes']['remaining'],
                'attempted' => $errors['minutes']['attempted']
            ]));
        }
    }

    public function getReadingProgress()
    {
        if (!$this->book->page_count) {
            return 0;
        }

        return min(100, ($this->getTotalPagesRead() / $this->book->page_count) * 100);
    }

    public function getReadingStreak()
    {
        $logs = $this->readingLogs->sortBy('log_date');
        $streak = 0;
        $currentDate = now()->startOfDay();

        foreach ($logs->reverse() as $log) {
            $logDate = Carbon::parse($log->log_date)->startOfDay();

            if ($logDate->eq($currentDate) || $logDate->eq($currentDate->copy()->subDay())) {
                $streak++;
                $currentDate = $logDate->copy()->subDay();
            } else {
                break;
            }
        }

        return $streak;
    }

    /**
     * Get daily reading statistics for display
     */
    public function getDailyStats()
    {
        $today = now()->toDateString();
        $todaysPagesRead = $this->getTodaysTotalPages($today);
        $todaysMinutesRead = $this->getTodaysTotalMinutes($today);

        return [
            'pages_read' => $todaysPagesRead,
            'pages_remaining' => max(0, self::DAILY_PAGES_LIMIT - $todaysPagesRead),
            'pages_limit' => self::DAILY_PAGES_LIMIT,
            'minutes_read' => $todaysMinutesRead,
            'minutes_remaining' => max(0, self::DAILY_MINUTES_LIMIT - $todaysMinutesRead),
            'minutes_limit' => self::DAILY_MINUTES_LIMIT,
        ];
    }

    public function render()
    {
        return view('livewire.mobile.reading-log');
    }
}
