<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
    <div class="max-w-md mx-auto">
        <!-- Book Preview Card -->
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <div class="flex items-start space-x-4">
                @if(!empty($bookData['cover_image']))
                    <img src="{{ $bookData['cover_image'] }}" 
                         alt="{{ $bookData['name'] }}" 
                         class="w-16 h-20 object-cover rounded-lg shadow-md">
                @else
                    <div class="w-16 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                @endif
                
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-800 text-sm leading-tight mb-1">
                        {{ $bookData['name'] }}
                    </h3>
                    
                    @if(!empty($bookData['author']))
                        <p class="text-xs text-gray-600 mb-1">
                            {{ is_array($bookData['author']) ? implode(', ', $bookData['author']) : $bookData['author'] }}
                        </p>
                    @endif
                    
                    @if(!empty($bookData['publisher']))
                        <p class="text-xs text-gray-500">{{ $bookData['publisher'] }}</p>
                    @endif                    
                </div>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-2xl shadow-lg p-6">
            
            <form wire:submit.prevent="createBook">

                <!-- Book Type Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        {{ __('mobile.select_book_type') }} <span class="text-red-500">*</span>
                    </label>
                    
<div class="space-y-2">
    @foreach($bookTypes as $bookType)
        <button
            type="button"
            wire:click="$set('selectedBookTypeId', {{ $bookType['id'] }})"
            class="w-full text-left flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors
                {{ $selectedBookTypeId == $bookType['id'] ? 'border-blue-500 bg-blue-50' : 'border-gray-200' }}"
        >
            <div class="flex-shrink-0 w-4 h-4 border-2 rounded-full mr-3 flex items-center justify-center
                {{ $selectedBookTypeId == $bookType['id'] ? 'border-blue-500 bg-blue-500' : 'border-gray-300' }}">
                @if($selectedBookTypeId == $bookType['id'])
                    <div class="w-2 h-2 bg-white rounded-full"></div>
                @endif
            </div>
            <div class="flex items-center">
                <img src="{{ asset('storage/' . $bookType['thumbnail']) }}"
                     alt="{{ $bookType['name'] }} ({{ $bookType['description'] }})"
                     class="h-12 mr-2">
                     
                <span class="text-sm font-medium text-gray-700 flex flex-col items-start">
                    <span class="font-bold">{{ $bookType['name'] }}</span>
                    <span class="text-xs text-gray-500">{{ $bookType['description'] }}</span>
                </span>
            </div>
        </button>
    @endforeach
</div>
                    
                    @error('selectedBookTypeId')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Manual Page Count (if not found during discovery) -->
                @if(empty($bookData['page_count']))
                    <div class="mb-6">
                        <label for="manualPageCount" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ __('mobile.enter_page_count') }}
                        </label>
                        <input type="number" 
                               id="manualPageCount"
                               wire:model="manualPageCount"
                               min="1" 
                               max="1000"
                               placeholder="{{ __('mobile.enter_page_count') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        
                        @error('manualPageCount')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        
                        <p class="mt-2 text-xs text-gray-500">
                            {{ __('mobile.page_count_help') }}
                        </p>
                    </div>
                @endif               

                <!-- Error Message -->
                @if($errorMessage)
                    <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm text-red-800">{{ $errorMessage }}</span>
                        </div>
                    </div>
                @endif

                <!-- Success Message -->
                @if($successMessage)
                    <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm text-green-800">{{ $successMessage }}</span>
                        </div>
                    </div>
                @endif

                <!-- Date Selection Widget -->
                <div class="mt-4 mb-6 border rounded-lg border-violet-200 p-4">
                    <p class="text-gray-700 mb-2 text-sm">{{ __('mobile.select_reading_start_date') }}</p>
                    <livewire:mobile.components.date-selection-widget
                        :selectedDate="now()"
                        :minDate="now()->subDays(7)"
                        :maxDate="now()"
                        wire:key="book-type-date-selector" />
                </div>

                <!-- Submit Button -->
                <button type="submit"
                        class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                        wire:loading.attr="disabled">
                    <div wire:loading.remove>
                        {{ __('mobile.add_book') }}
                    </div>
                    <div wire:loading class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ __('mobile.creating_book') }}
                    </div>
                </button>
            </form>
        </div>
    </div>
</div>
