[2025-10-30 13:16:13] local.DEBUG: Local repository: JSON file not found {"isbn":"9786051141190","path":"book_repository/json/9786051141190.json"} 
[2025-10-30 13:16:13] local.INFO: Searching with provider: D&R {"isbn":"9786051141190"} 
[2025-10-30 13:16:15] local.INFO: Book found with provider: D&R {"isbn":"9786051141190","name":"Buluşlar ve Serüvenleri - Bilgisayar"} 
[2025-10-30 13:16:15] local.INFO: Creating book from discovery data {"isbn":"9786051141190","data":{"name":"Buluşlar ve Serüvenleri - Bilgisayar","author":["Kolektif"],"publisher":"<PERSON><PERSON> Tim<PERSON>ş","year":2010,"isbn":"9786051141190","page_count":64,"cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0000000325100-1.jpg","source":"D&R"}} 
[2025-10-30 13:16:16] local.INFO: Book created from discovery {"book_id":55,"name":"Buluşlar ve Serüvenleri - Bilgisayar","isbn":"9786051141190","source":"D&R"} 
[2025-10-30 13:16:16] local.INFO: Book created successfully from discovery service {"book_id":55,"isbn":"9786051141190","source":"D&R"} 
[2025-11-01 17:30:46] local.DEBUG: Local repository: JSON file not found {"isbn":"9786259861630","path":"book_repository/json/9786259861630.json"} 
[2025-11-01 17:30:46] local.INFO: Searching with provider: D&R {"isbn":"9786259861630"} 
[2025-11-01 17:30:47] local.INFO: Book found with provider: D&R {"isbn":"9786259861630","name":"Bir Direniş Çizgi Romanı"} 
[2025-11-01 17:30:47] local.INFO: Creating book from discovery data {"isbn":"9786259861630","data":{"name":"Bir Direniş Çizgi Romanı","author":["If Moses"],"publisher":"Hüdhüd Kitap","page_count":112,"year":2024,"isbn":"9786259861630","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0002136393001-1.jpg","source":"D&R"}} 
[2025-11-01 17:30:47] local.INFO: Book created from discovery {"book_id":56,"name":"Bir Direniş Çizgi Romanı","isbn":"9786259861630","source":"D&R"} 
[2025-11-01 17:30:47] local.INFO: Book created successfully from discovery service {"book_id":56,"isbn":"9786259861630","source":"D&R"} 
[2025-11-01 17:31:38] local.DEBUG: Local repository: JSON file not found {"isbn":"9786259411514","path":"book_repository/json/9786259411514.json"} 
[2025-11-01 17:31:38] local.INFO: Searching with provider: D&R {"isbn":"9786259411514"} 
[2025-11-01 17:31:40] local.INFO: Book found with provider: D&R {"isbn":"9786259411514","name":"Gazze - Bir Direniş Çizgi Romanı"} 
[2025-11-01 17:31:40] local.INFO: Creating book from discovery data {"isbn":"9786259411514","data":{"name":"Gazze - Bir Direniş Çizgi Romanı","author":["If Moses"],"publisher":"Hüdhüd Kitap","page_count":112,"year":2024,"isbn":"9786259411514","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0002096612001-1.jpg","source":"D&R"}} 
[2025-11-01 17:31:40] local.INFO: Book created from discovery {"book_id":57,"name":"Gazze - Bir Direniş Çizgi Romanı","isbn":"9786259411514","source":"D&R"} 
[2025-11-01 17:31:40] local.INFO: Book created successfully from discovery service {"book_id":57,"isbn":"9786259411514","source":"D&R"} 
[2025-11-01 23:36:54] local.ERROR: FCM API error: {
  "error": {
    "code": 404,
    "message": "Requested entity was not found.",
    "status": "NOT_FOUND",
    "details": [
      {
        "@type": "type.googleapis.com/google.firebase.fcm.v1.FcmError",
        "errorCode": "UNREGISTERED"
      }
    ]
  }
}
  
[2025-11-01 23:37:58] local.ERROR: FCM API error: {
  "error": {
    "code": 404,
    "message": "Requested entity was not found.",
    "status": "NOT_FOUND",
    "details": [
      {
        "@type": "type.googleapis.com/google.firebase.fcm.v1.FcmError",
        "errorCode": "UNREGISTERED"
      }
    ]
  }
}
  
[2025-11-02 10:49:42] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(390): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1154): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(972): Illuminate\\Container\\Container->build()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(903): Illuminate\\Foundation\\Application->resolve()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1379): Illuminate\\Foundation\\Application->make()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1278): Illuminate\\Container\\Container->resolveClass()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1201): Illuminate\\Container\\Container->resolveDependencies()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(972): Illuminate\\Container\\Container->build()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(903): Illuminate\\Foundation\\Application->resolve()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Application->make()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#47 {main}
"} 
[2025-11-02 10:49:43] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(390): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1154): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(972): Illuminate\\Container\\Container->build()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(903): Illuminate\\Foundation\\Application->resolve()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1379): Illuminate\\Foundation\\Application->make()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1278): Illuminate\\Container\\Container->resolveClass()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1201): Illuminate\\Container\\Container->resolveDependencies()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(972): Illuminate\\Container\\Container->build()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(903): Illuminate\\Foundation\\Application->resolve()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1222): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#20 {main}
"} 
[2025-11-02 10:51:26] local.INFO: UserBook session automatically completed {"user_id":9,"book_id":24,"session_id":46,"completion_date":"2025-10-31 00:00:00","reading_log_id":186} 
[2025-11-02 13:26:46] local.DEBUG: Local repository: JSON file not found {"isbn":"*************","path":"book_repository/json/*************.json"} 
[2025-11-02 13:26:46] local.INFO: Searching with provider: D&R {"isbn":"*************"} 
[2025-11-02 13:26:48] local.INFO: Book found with provider: D&R {"isbn":"*************","name":"Orman Kitabı-Kısaltılmış Metin-İş Çocuk Klasikleri"} 
[2025-11-02 13:26:48] local.INFO: Creating book from discovery data {"isbn":"*************","data":{"name":"Orman Kitabı-Kısaltılmış Metin-İş Çocuk Klasikleri","author":["Rudyard Kipling"],"publisher":"İş Bankası Kültür Yayınları","year":2019,"isbn":"*************","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/*************-1.jpg","source":"D&R"}} 
[2025-11-02 13:26:48] local.INFO: Book created from discovery {"book_id":58,"name":"Orman Kitabı-Kısaltılmış Metin-İş Çocuk Klasikleri","isbn":"*************","source":"D&R"} 
[2025-11-02 13:26:48] local.INFO: Book created successfully from discovery service {"book_id":58,"isbn":"*************","source":"D&R"} 
[2025-11-02 13:29:25] local.DEBUG: Local repository: JSON file not found {"isbn":"*************","path":"book_repository/json/*************.json"} 
[2025-11-02 13:29:25] local.INFO: Searching with provider: D&R {"isbn":"*************"} 
[2025-11-02 13:29:26] local.INFO: Book found with provider: D&R {"isbn":"*************","name":"Bebek Annem"} 
[2025-11-02 13:30:01] local.INFO: Book created from discovery {"book_id":59,"name":"Bebek Annem","isbn":"*************","source":"D&R"} 
[2025-11-02 13:30:01] local.INFO: Book created successfully via discovery service {"book_id":59,"user_id":9,"isbn":"*************","user_book_created":true} 
[2025-11-02 13:31:40] local.DEBUG: Local repository: JSON file not found {"isbn":"9786059952248","path":"book_repository/json/9786059952248.json"} 
[2025-11-02 13:31:40] local.INFO: Searching with provider: D&R {"isbn":"9786059952248"} 
[2025-11-02 13:31:41] local.INFO: Book found with provider: D&R {"isbn":"9786059952248","name":"Eve Giden Küçük Tren"} 
[2025-11-02 13:34:47] local.INFO: Book created from discovery {"book_id":60,"name":"Eve Giden Küçük Tren","isbn":"9786059952248","source":"D&R"} 
[2025-11-02 13:34:48] local.INFO: Book created successfully via discovery service {"book_id":60,"user_id":9,"isbn":"9786059952248","user_book_created":true} 
[2025-11-02 23:04:57] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:04:57] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:04:58] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:26:03] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:26:03] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:26:03] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:26:09] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:26:09] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:26:09] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:27:53] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:27:53] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:27:53] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:28:03] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:28:03] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:28:03] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:34:46] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:34:46] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:34:46] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:35:06] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:35:06] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:35:06] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:37:48] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:37:48] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:37:48] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:38:39] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:38:39] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:38:39] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:38:49] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:38:49] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:38:49] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:39:06] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:39:06] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:39:06] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:41:54] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:41:54] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:41:54] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:42:16] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:42:16] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:42:16] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:42:41] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:42:41] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:42:41] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:43:15] local.DEBUG: Local repository: JSON file not found {"isbn":"9786057185747","path":"book_repository/json/9786057185747.json"} 
[2025-11-02 23:43:15] local.INFO: Searching with provider: D&R {"isbn":"9786057185747"} 
[2025-11-02 23:43:15] local.INFO: Book found with provider: D&R {"isbn":"9786057185747","name":"Başım Dertte"} 
[2025-11-02 23:44:17] local.INFO: Book created from discovery {"book_id":61,"name":"Başım Dertte","isbn":"9786057185747","source":"D&R"} 
[2025-11-02 23:44:17] local.INFO: Book created successfully via discovery service {"book_id":61,"user_id":9,"isbn":"9786057185747","user_book_created":true} 
