<?php

namespace App\Livewire\Mobile\Components;

use Livewire\Component;
use Carbon\Carbon;

class DateSelectionWidget extends Component
{
    public $selectedDate;
    public $minDate = null;
    public $maxDate = null;
    public $dateIndex = 0; // 0 = today, 1 = yesterday, etc.

    public function mount($selectedDate = null, $minDate = null, $maxDate = null)
    {
        // Set max date to today
        $this->maxDate = $maxDate ? Carbon::parse($maxDate) : now();

        // Set min date - ensure it's not more than 7 days ago
        $defaultMinDate = now()->subDays(7);
        if ($minDate) {
            $providedMinDate = Carbon::parse($minDate);
            // Use the later of the two dates (closer to today)
            $this->minDate = $providedMinDate->greaterThan($defaultMinDate) ? $providedMinDate : $defaultMinDate;
        } else {
            $this->minDate = $defaultMinDate;
        }

        // Initialize selected date to today
        $this->selectedDate = $selectedDate ? Carbon::parse($selectedDate) : now();

        // Ensure selected date is within bounds
        $this->validateAndAdjustDate();

        // Calculate date index
        $this->calculateDateIndex();

        // Emit initial date selection
        $this->emitDateChange();
    }

    public function previousDate()
    {
        // Previous = go back in time (older date)
        $newDate = $this->selectedDate->copy()->subDay();

        if ($newDate->greaterThanOrEqualTo($this->minDate)) {
            $this->selectedDate = $newDate;
            $this->calculateDateIndex();
            $this->emitDateChange();
        }
    }

    public function nextDate()
    {
        // Next = go forward in time (newer date)
        $newDate = $this->selectedDate->copy()->addDay();

        if ($newDate->lessThanOrEqualTo($this->maxDate)) {
            $this->selectedDate = $newDate;
            $this->calculateDateIndex();
            $this->emitDateChange();
        }
    }

    public function getDateDisplayText()
    {
        $daysDiff = now()->startOfDay()->diffInDays($this->selectedDate->startOfDay());        
        $daysDiff = abs(intval($daysDiff));

        if ($daysDiff === 0) {
            return __('mobile.today');
        } elseif ($daysDiff === 1) {
            return __('mobile.yesterday');
        } else {
            return __('mobile.days_ago', ['count' => $daysDiff]);
        }
    }

    public function canGoToPrevious()
    {
        // Can go to previous (older) date
        return $this->selectedDate->copy()->subDay()->greaterThanOrEqualTo($this->minDate);
    }

    public function canGoToNext()
    {
        // Can go to next (newer) date
        return $this->selectedDate->copy()->addDay()->lessThanOrEqualTo($this->maxDate);
    }

    private function calculateDateIndex()
    {
        $this->dateIndex = now()->startOfDay()->diffInDays($this->selectedDate->startOfDay());
    }

    private function validateAndAdjustDate()
    {
        if ($this->selectedDate->lessThan($this->minDate)) {
            $this->selectedDate = $this->minDate->copy();
        }

        if ($this->selectedDate->greaterThan($this->maxDate)) {
            $this->selectedDate = $this->maxDate->copy();
        }
    }

    private function emitDateChange()
    {
        $this->dispatch('dateSelected', [
            'date' => $this->selectedDate->toDateString(),
            'dateTime' => $this->selectedDate->toDateTimeString(),
            'dateIndex' => $this->dateIndex
        ]);
    }

    public function render()
    {
        return view('livewire.mobile.components.date-selection-widget');
    }
}
