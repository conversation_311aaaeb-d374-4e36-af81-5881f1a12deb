<div class="flex items-center justify-between bg-white rounded-2xl p-1">
    <!-- Left Button = Next Day (Forward in Time) -->
    <button
        type="button"
        wire:click="nextDate"
        class="flex items-center justify-center w-10 h-10 rounded-full bg-violet-400 text-white hover:bg-violet-600 transition-colors {{ !$this->canGoToNext() ? 'opacity-50 cursor-not-allowed' : '' }}"
        {{ !$this->canGoToNext() ? 'disabled' : '' }}
    >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
    </button>

    <!-- Date Display -->
    <div class="flex-1 text-center">
        <span class="text-violet-600 font-semibold text-lg">
            {{ $this->getDateDisplayText() }}
        </span>
    </div>

    <!-- Right Button = Previous Day (Back in Time) -->
    <button
        type="button"
        wire:click="previousDate"
        class="flex items-center justify-center w-10 h-10 rounded-full bg-violet-400 text-white hover:bg-violet-600  transition-colors {{ !$this->canGoToPrevious() ? 'opacity-50 cursor-not-allowed' : '' }}"
        {{ !$this->canGoToPrevious() ? 'disabled' : '' }}
    >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
    </button>
</div>
